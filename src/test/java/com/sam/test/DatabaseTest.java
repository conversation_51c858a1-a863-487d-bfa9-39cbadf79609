package com.sam.test;

import com.sam.dao.impl.HouseDAOImpl;
import com.sam.entity.CommunityInfo;
import com.sam.util.DBUtil;

import java.sql.Connection;

/**
 * 数据库连接和插入测试类
 */
public class DatabaseTest {
    
    public static void main(String[] args) {
        System.out.println("=== 开始数据库测试 ===");
        
        // 1. 测试数据库连接
        testConnection();
        
        // 2. 测试小区插入
        testInsertCommunity();
        
        System.out.println("=== 数据库测试完成 ===");
    }
    
    /**
     * 测试数据库连接
     */
    public static void testConnection() {
        System.out.println("\n--- 测试数据库连接 ---");
        
        try {
            boolean isConnected = DBUtil.testConnection();
            if (isConnected) {
                System.out.println("✅ 数据库连接测试成功！");
            } else {
                System.out.println("❌ 数据库连接测试失败！");
            }
        } catch (Exception e) {
            System.out.println("❌ 数据库连接测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试小区插入
     */
    public static void testInsertCommunity() {
        System.out.println("\n--- 测试小区插入 ---");
        
        try {
            HouseDAOImpl dao = new HouseDAOImpl();
            
            // 创建测试数据
            CommunityInfo community = new CommunityInfo();
            community.setCname("测试小区" + System.currentTimeMillis());
            community.setCpic("https://test-image-url.jpg");
            community.setCaddress("测试地址123号");
            
            System.out.println("准备插入测试数据:");
            System.out.println("小区名称: " + community.getCname());
            System.out.println("图片路径: " + community.getCpic());
            System.out.println("小区地址: " + community.getCaddress());
            
            // 执行插入
            int result = dao.insertCommunity(community);
            
            if (result > 0) {
                System.out.println("✅ 小区插入成功！插入行数: " + result);
                System.out.println("生成的小区ID: " + community.getCid());
            } else {
                System.out.println("❌ 小区插入失败！返回值: " + result);
            }
            
        } catch (Exception e) {
            System.out.println("❌ 小区插入测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
