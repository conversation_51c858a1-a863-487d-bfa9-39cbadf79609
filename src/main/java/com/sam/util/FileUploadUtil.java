package com.sam.util;


import com.sam.exception.FileUploadException;
import jakarta.servlet.http.Part;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

/**
 * 文件上传工具类
 */
public class FileUploadUtil {

    // 配置文件存储根目录（建议通过配置中心管理）
    public static final String UPLOAD_ROOT = "/Users/<USER>/Downloads/Javaproject/DevImg";

    private FileUploadUtil() {
    } // 防止实例化

    /**
     * 安全文件上传方法
     *
     * @param relativeDir 相对存储目录（如：/vote_system）
     * @param part       附件 <input type="file">
     * @return           相对存储路径
     * @throws FileUploadException 自定义上传异常
     */
    public static String upload(String relativeDir, Part part) throws FileUploadException {
        try {

            // 1. 创建安全存储路径
            PathUtil.validatePath(relativeDir);
            File targetDir = new File(UPLOAD_ROOT, relativeDir);
            if (!targetDir.exists() && !targetDir.mkdirs()) {
                throw new FileUploadException("存储目录创建失败!");
            }

            // 2. 生成安全文件名  img.jpg
            String originalName = PathUtil.cleanPath(
                    new File(part.getSubmittedFileName()).getName()); // 防御路径遍历
            // jpg
            String extension = originalName.substring(originalName.lastIndexOf(".")+1);
            // 新文件  UUID.jpg
            String newFileName = UUID.randomUUID() + "." + extension.toLowerCase();

            // 3. 存储文件
            File targetFile = new File(targetDir, newFileName);
            // 使用NIO进行高效的文件存储
            Files.copy(part.getInputStream(), targetFile.toPath(), StandardCopyOption.REPLACE_EXISTING);

            // 4. 输出提示信息，输出绝对路径
            System.out.println("文件上传成功(绝对路径) = " + targetFile.getAbsolutePath());

            // 返回相对路径
            return PathUtil.normalize(relativeDir + "/img/" + newFileName);
        } catch (IOException e) {
            System.out.println("文件上传失败"+e);
            throw new FileUploadException("文件上传失败", e);
        }
    }
}



/**
 * 路径处理工具类
 */
class PathUtil {
    public static String normalize(String path) {
        return path.replace('\\', '/').replaceAll("/+", "/");
    }

    public static String cleanPath(String path) {
        return normalize(new File(path).getName());
    }

    public static void validatePath(String relativePath) throws FileUploadException {
        if (relativePath.contains("..")) {
            throw new FileUploadException("Path traversal attempt detected");
        }
    }
}