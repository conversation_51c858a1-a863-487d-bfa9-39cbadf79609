package com.sam.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.PutObjectRequest;

import java.io.File;
import java.io.InputStream;
import java.util.Properties;

/**
 * 将文件上传到阿里云 OSS
 */
public class OSSUtil {

    private static Properties ossProperties;

    // 静态初始化配置
    static {
        try {
            ossProperties = new Properties();
            InputStream is = OSSUtil.class.getClassLoader().getResourceAsStream("oss.properties");
            if (is != null) {
                ossProperties.load(is);
            } else {
                System.err.println("警告: 未找到 oss.properties 配置文件，使用默认配置");
                // 设置默认配置
                ossProperties.setProperty("oss.accessKeyId", "LTAI5tKhzE4C5vHsC9ZSFRcD");
                ossProperties.setProperty("oss.accessKeySecret", "******************************");
                ossProperties.setProperty("oss.bucketName", "sampoterbucket");
                ossProperties.setProperty("oss.endpoint", "https://oss-cn-wuhan-lr.aliyuncs.com");
                ossProperties.setProperty("oss.folder", "vote-system/uploads/");
            }
        } catch (Exception e) {
            throw new RuntimeException("OSS配置初始化失败", e);
        }
    }

    // 获取配置信息
    private static String getAccessKeyId() {
        return ossProperties.getProperty("oss.accessKeyId");
    }

    private static String getAccessKeySecret() {
        return ossProperties.getProperty("oss.accessKeySecret");
    }

    public static String getBucketName() {
        return ossProperties.getProperty("oss.bucketName");
    }

    private static String getEndpoint() {
        return ossProperties.getProperty("oss.endpoint");
    }

    private static String getFolder() {
        return ossProperties.getProperty("oss.folder", "uploads/");
    }

    /**
     * 上传文件到阿里云 OSS
     * @param fileKey  OSS 上的文件路径
     * @param filePath 本地文件路径
     * @return 文件的访问URL
     */
    public static String upload(String fileKey, String filePath) {
        OSS ossClient = null;
        try {
            // 1. 验证配置
            String accessKeyId = getAccessKeyId();
            String accessKeySecret = getAccessKeySecret();
            String bucketName = getBucketName();
            String endpoint = getEndpoint();

            if (accessKeyId == null || accessKeySecret == null || bucketName == null || endpoint == null) {
                throw new RuntimeException("OSS配置不完整，请检查配置文件");
            }

            // 2. 创建 OSSClient 实例
            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

            // 3. 验证文件是否存在
            File localFile = new File(filePath);
            if (!localFile.exists()) {
                throw new RuntimeException("本地文件不存在: " + filePath);
            }

            // 4. 添加文件夹前缀
            String fullFileKey = getFolder() + fileKey;

            // 5. 创建 PutObjectRequest
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fullFileKey, localFile);

            // 6. 上传文件
            ossClient.putObject(putObjectRequest);

            // 7. 设置文件为公共读取（可选，根据需要）
            try {
                ossClient.setObjectAcl(bucketName, fullFileKey, CannedAccessControlList.PublicRead);
            } catch (Exception e) {
                System.out.println("警告: 无法设置文件公共读取权限，可能需要手动设置: " + e.getMessage());
            }

            // 8. 生成访问URL
            String fileUrl = "https://" + bucketName + "." + endpoint.replace("https://", "") + "/" + fullFileKey;
            System.out.println("文件上传成功！访问地址：" + fileUrl);

            return fileUrl;
        } catch (Exception e) {
            System.err.println("OSS上传失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("OSS上传失败", e);
        } finally {
            // 9. 关闭 OSSClient
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 检查OSS连接是否正常
     * @return true表示连接正常
     */
    public static boolean testConnection() {
        OSS ossClient = null;
        try {
            String accessKeyId = getAccessKeyId();
            String accessKeySecret = getAccessKeySecret();
            String endpoint = getEndpoint();

            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

            // 尝试列出bucket（这是一个轻量级的测试操作）
            ossClient.listBuckets();

            System.out.println("OSS连接测试成功");
            return true;
        } catch (Exception e) {
            System.err.println("OSS连接测试失败: " + e.getMessage());
            return false;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

}