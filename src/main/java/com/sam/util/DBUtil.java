package com.sam.util;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-13 18:45
 */
import java.io.InputStream;
import java.sql.*;
import java.util.Properties;

public class DBUtil {
    public static final String URL;
    public static final String DRIVER;
    public static final String USER;
    public static final String PASSWORD;

    static {
        try {
            Properties prop = new Properties();
            InputStream is = DBUtil.class.getClassLoader().getResourceAsStream("jdbc.properties");
            prop.load(is);
            URL = prop.getProperty("url");
            DRIVER = prop.getProperty("driverClass");
            USER = prop.getProperty("user");
            PASSWORD = prop.getProperty("password");
            Class.forName(DRIVER);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取连接对象
     *
     * @return 数据库连接对象
     */
    public static Connection getConnection() {
        Connection conn = null;
        try {
            System.out.println("尝试连接数据库: " + URL);
            conn = DriverManager.getConnection(URL, USER, PASSWORD);
            System.out.println("数据库连接成功！");
        } catch (SQLException e) {
            System.err.println("数据库连接失败: " + e.getMessage());
            System.err.println("URL: " + URL);
            System.err.println("USER: " + USER);
            e.printStackTrace();
        }
        return conn;
    }

    /**
     * 测试数据库连接
     */
    public static boolean testConnection() {
        try (Connection conn = getConnection()) {
            return conn != null && !conn.isClosed();
        } catch (SQLException e) {
            System.err.println("测试数据库连接失败: " + e.getMessage());
            return false;
        }
    }
}