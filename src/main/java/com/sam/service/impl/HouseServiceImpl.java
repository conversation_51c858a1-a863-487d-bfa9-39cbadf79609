package com.sam.service.impl;

import com.sam.dao.IHouseDAO;
import com.sam.dao.impl.HouseDAOImpl;
import com.sam.entity.BuildingInfo;
import com.sam.entity.CommunityInfo;
import com.sam.service.IHouseService;
import com.sam.util.ResultVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-13 18:50
 */
public class HouseServiceImpl implements IHouseService {

    private IHouseDAO houseDAO = new HouseDAOImpl();

    @Override
    public ResultVO<List<CommunityInfo>> findAllCommunity() {
        List<CommunityInfo> list = houseDAO.selectAllCommunity();
        return ResultVO.success(list);
    }

    @Override
    public ResultVO<List<BuildingInfo>> findBuildingByCommunityId(Integer cid) {
        List<BuildingInfo> list = houseDAO.selectBuildingByCommunityId(cid);
        return ResultVO.success(list);
    }

    @Override
    public ResultVO<?> insertCommunity(CommunityInfo communityInfo) {
        if(communityInfo == null) {
            return ResultVO.error("参数错误");
        }
        int num = houseDAO.insertCommunity(communityInfo);
        if (num > 0) {
            return ResultVO.success("插入成功");
        } else {
            return ResultVO.error("插入失败");
        }
    }

    @Override
    public ResultVO<?> deleteCommunity(Integer communityId) {
        int num = houseDAO.deleteCommunity(communityId);
        return num > 0 ? ResultVO.success("删除成功") : ResultVO.error("删除失败");
    }

    @Override
    public ResultVO<?> insertBuilding(BuildingInfo buildingInfo) {
        if(buildingInfo == null){
            return ResultVO.error("参数错误");
        }
        int num = houseDAO.insertBuilding(buildingInfo);
        return num > 0 ? ResultVO.success() : ResultVO.error("插入失败");
    }

    @Override
    public ResultVO<?> deleteBuilding(String did, Integer cid) {
        int num = houseDAO.deleteBuilding(did, cid);
        return num > 0 ? ResultVO.success("删除成功") : ResultVO.error("删除失败");
    }
}
