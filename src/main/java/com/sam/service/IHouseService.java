package com.sam.service;

import com.sam.entity.BuildingInfo;
import com.sam.entity.CommunityInfo;
import com.sam.util.ResultVO;

import java.util.List;

public interface IHouseService {

    //查询所有小区,列出所有小区信息
    ResultVO<List<CommunityInfo>> findAllCommunity();

    //根据小区ID查询楼栋
    ResultVO<List<BuildingInfo>> findBuildingByCommunityId(Integer cid);

    //插入单个小区
    ResultVO<?> insertCommunity(CommunityInfo communityInfo);

    //删除单个小区（小区内所有的楼栋信息会一并删除）
    ResultVO<?> deleteCommunity(Integer communityId);

    //插入单个楼栋
    ResultVO<?> insertBuilding(BuildingInfo buildingInfo);

    //删除单个楼栋
    ResultVO<?> deleteBuilding(String did, Integer cid);

}
