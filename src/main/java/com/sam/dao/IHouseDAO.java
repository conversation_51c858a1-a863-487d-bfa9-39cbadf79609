package com.sam.dao;

import com.sam.entity.BuildingInfo;
import com.sam.entity.CommunityInfo;

import java.util.List;

public interface IHouseDAO {

    //查询所有小区信息
    List<CommunityInfo> selectAllCommunity();
    //根据CID查询所有楼栋信息
    List<BuildingInfo> selectBuildingByCommunityId(Integer cid);
    //添加小区
    int insertCommunity(CommunityInfo communityInfo);
    //删除小区
    int deleteCommunity(Integer cid);
    //添加楼栋
    int insertBuilding(BuildingInfo buildingInfo);
    //删除楼栋
    int deleteBuilding(String did, Integer cid);
}
