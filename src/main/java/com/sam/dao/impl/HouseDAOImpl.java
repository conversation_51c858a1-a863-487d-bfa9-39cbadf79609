package com.sam.dao.impl;

import com.sam.dao.IHouseDAO;
import com.sam.entity.BuildingInfo;
import com.sam.entity.CommunityInfo;
import com.sam.util.DBUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-13 18:50
 */

public class HouseDAOImpl implements IHouseDAO {

    @Override
    public List<CommunityInfo> selectAllCommunity() {
        List<CommunityInfo> list = new ArrayList<>();
        String sql = "SELECT cid, cname, cpic, caddress FROM community";

        try (
             Connection conn = DBUtil.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql);
             ResultSet rs = ps.executeQuery()
        ) {
            while (rs.next()) {
                CommunityInfo c = new CommunityInfo();
                c.setCid(rs.getInt("cid"));
                c.setCname(rs.getString("cname"));
                c.setCpic(rs.getString("cpic"));
                c.setCaddress(rs.getString("caddress"));
                list.add(c);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return list;
    }


    @Override
    public List<BuildingInfo> selectBuildingByCommunityId(Integer cid) {
        List<BuildingInfo> list = new ArrayList<>();
        String sql = "SELECT did, cid, height FROM building WHERE cid = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setInt(1, cid);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    BuildingInfo b = new BuildingInfo();
                    b.setDid(rs.getString("did"));
                    b.setCid(rs.getInt("cid"));
                    b.setHeight(rs.getInt("height"));
                    list.add(b);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public int insertCommunity(CommunityInfo communityInfo) {
        String sql = "INSERT INTO community(cname, cpic, caddress) VALUES(?, ?, ?)";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            ps.setString(1, communityInfo.getCname());
            ps.setString(2, communityInfo.getCpic());
            ps.setString(3, communityInfo.getCaddress());

            int rows = ps.executeUpdate();
            if (rows > 0) {
                try (ResultSet rs = ps.getGeneratedKeys()) {
                    if (rs.next()) {
                        communityInfo.setCid(rs.getInt(1)); // 设置自增ID
                    }
                }
            }
            return rows;

        } catch (SQLException e) {
            e.printStackTrace();
            return 0;
        }
    }

    @Override
    public int deleteCommunity(Integer cid) {
        String sql = "DELETE FROM community WHERE cid = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.setInt(1, cid);
            return ps.executeUpdate();

        } catch (SQLException e) {
            e.printStackTrace();
            return 0;
        }
    }

    @Override
    public int insertBuilding(BuildingInfo buildingInfo) {
        String sql = "INSERT INTO building(did, cid, height) VALUES(?, ?, ?)";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.setString(1, buildingInfo.getDid());
            ps.setInt(2, buildingInfo.getCid());
            ps.setInt(3, buildingInfo.getHeight());

            return ps.executeUpdate();

        } catch (SQLException e) {
            e.printStackTrace();
            return 0;
        }
    }

    @Override
    public int deleteBuilding(String did, Integer cid) {
        String sql = "DELETE FROM building WHERE did = ? AND cid = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.setString(1, did);
            ps.setInt(2, cid);

            return ps.executeUpdate();

        } catch (SQLException e) {
            e.printStackTrace();
            return 0;
        }
    }
}