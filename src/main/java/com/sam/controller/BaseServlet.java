package com.sam.controller;

import cn.hutool.core.util.ReflectUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2025/8/13
 */

/**
 * 模仿SpringMVC框架的Controller类，一个类可以处理多个请求
 * ManagerServlet继承 BaseServlet , BaseServlet 继承 HttpServlet
 */
public class BaseServlet extends HttpServlet {
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doPost(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        String ip = request.getRemoteAddr();
        request.setCharacterEncoding("utf-8");
        String methodName = request.getParameter("to");
        Class<? extends BaseServlet> clazz = this.getClass();
        String servletName = clazz.getName();
        System.out.println(ip+"正在访问" + servletName+"的" + methodName+"方法!");
        try {
            Method method = ReflectUtil.getMethod(clazz,
                    methodName, HttpServletRequest.class, HttpServletResponse.class);
            method.invoke(this,request,response);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(ip+"你的请求方法写错误:请检查你的"+servletName+"类中是否有这个"+methodName+"方法!!!");
            response.setCharacterEncoding("UTF-8");
            response.setContentType("text/html;charset=UTF-8");
            PrintWriter pw = response.getWriter();
            pw.println(ip+"你的请求方法写错误:请检查你的"+servletName+"类中是否有这个"+methodName+"方法!!!"
                    +e.getClass()+e.getMessage());
            pw.flush();
            pw.close();
        }
    }
}

