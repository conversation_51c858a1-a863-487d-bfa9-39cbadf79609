package com.sam.controller;

import jakarta.servlet.Servlet;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

@WebServlet(name = "PageServlet", value = "/PageServlet")
public class PageServlet extends BaseServlet {
    /**
     * 跳转到小区信息页面
     */
    protected void CInfo_jsp(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.getRequestDispatcher("WEB-INF/views/CInfo.jsp").forward(request,response);
        System.out.println("跳转到小区信息页面");
    }

    /**
     * 跳转到楼栋信息页面
     */
    protected void BInfo_jsp(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.getRequestDispatcher("WEB-INF/views/BInfo.jsp").forward(request,response);
        System.out.println("跳转到楼栋信息页面");
    }

}