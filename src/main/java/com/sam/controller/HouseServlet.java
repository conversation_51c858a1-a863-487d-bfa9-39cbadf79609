package com.sam.controller;

import com.sam.entity.BuildingInfo;
import com.sam.entity.CommunityInfo;
import com.sam.service.IHouseService;
import com.sam.service.impl.HouseServiceImpl;
import com.sam.util.FileUploadUtil;
import com.sam.util.OSSUtil;
import com.sam.util.ResultVO;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.MultipartConfig;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.Part;

import java.io.File;
import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/8/13
 */
@WebServlet(name = "HouseServlet", value = "/HouseServlet")
@MultipartConfig(maxFileSize = 1024 * 1024 * 5) // 限制上传文件大小为5MB
public class HouseServlet extends BaseServlet {
    private IHouseService houseService = new HouseServiceImpl();

    /**
     * 插入小区
     */
    protected void insertCommunity(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {


    }

    /**
     * 查询所有小区信息
     */
    protected void findAllCommunity(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

    }

    /**
     * 根据小区ID查询楼栋
     */
    protected void findBuildingByCommunityId(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

    }

    /**
     * 删除小区
     */
    protected void deleteCommunity(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

    }

    /**
     * 插入楼栋
     */
    protected void insertBuilding(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {


    }

    /**
     * 删除楼栋
     */
    protected void deleteBuilding(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

    }
}