package com.sam.controller;
import com.sam.entity.CommunityInfo;
import com.sam.service.IHouseService;
import com.sam.service.impl.HouseServiceImpl;
import com.sam.util.FileUploadUtil;
import com.sam.util.OSSUtil;
import com.sam.util.ResultVO;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.MultipartConfig;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.Part;

import java.io.File;
import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/8/13
 */
@WebServlet(name = "HouseServlet", value = "/HouseServlet")
@MultipartConfig(maxFileSize = 1024 * 1024 * 5) // 限制上传文件大小为5MB
public class HouseServlet extends BaseServlet {
    private IHouseService houseService = new HouseServiceImpl();

    /**
     * 插入小区
     */
    protected void insertCommunity(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        String cname = request.getParameter("cname");
        String caddress = request.getParameter("caddress");
//        String cpic = "";
//        Part part = request.getPart("cpic");
//
//        if (Objects.nonNull(part) && part.getSize() > 0) {
//            try {
//                String relativePath = FileUploadUtil.upload(request.getContextPath(), part);
//                // 通过相对路径，获取文件名
//                String fileName = relativePath.substring(relativePath.lastIndexOf("/") + 1);
//                // 真实路径
//                File file = new File(FileUploadUtil.UPLOAD_ROOT
//                        + request.getContextPath()
//                        + File.separator
//                        + fileName);
//
//                // 检查文件是否存在
//                if (file.exists()) {
//                    //再将本地图片上传到云服务器
//                    cpic = OSSUtil.upload(fileName, file.getAbsolutePath());
//                    System.out.println("头像上传成功，OSS地址: " + cpic);
//                } else {
//                    System.out.println("警告: 本地文件不存在，跳过OSS上传: " + file.getAbsolutePath());
//                    // 使用本地相对路径作为备选
//                    cpic = request.getContextPath() + "/" + relativePath;
//                }
//            } catch (Exception e) {
//                System.err.println("头像上传到OSS失败: " + e.getMessage());
//                e.printStackTrace();
//                // 上传失败时使用本地路径
//                cpic = "";
//            }
//        }
        //new一个新的对象
        CommunityInfo communityInfo = new CommunityInfo();
        communityInfo.setCname(cname);
        communityInfo.setCaddress(caddress);
        ResultVO<?> result = houseService.insertCommunity(communityInfo);
//        response.getWriter().write(result.toString());

        if(result.getCode() == 200){
            request.getRequestDispatcher("WEB-INF/views/CInfo.jsp").forward(request, response);
        }else{
            request.setAttribute("tip", "输入信息错误，请重新输入");
        }
    }
    /**
     * 查询所有小区信息
     */
    /**
     * 查询所有小区信息
     */
    protected void findAllCommunity(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        ResultVO<?> result = houseService.findAllCommunity();

        if (result.getCode() == 200) {
            // 把小区列表放到 request
            request.setAttribute("communityList", result.getData());
            // 转发到 JSP
            request.getRequestDispatcher("WEB-INF/views/CInfo.jsp").forward(request, response);
        } else {
            request.setAttribute("tip", "查询错误");
            request.getRequestDispatcher("WEB-INF/views/CInfo.jsp").forward(request, response);
        }
    }

    /**
     * 根据小区ID查询楼栋
     */
    protected void findBuildingByCommunityId(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        String cidStr = request.getParameter("cid");
    }

    /**
     * 删除小区
     */
    protected void deleteCommunity(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

    }

    /**
     * 插入楼栋
     */
    protected void insertBuilding(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

    }
    /**
     * 删除楼栋
     */
    protected void deleteBuilding(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

    }
    
}