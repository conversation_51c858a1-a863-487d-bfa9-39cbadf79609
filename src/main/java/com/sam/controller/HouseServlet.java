package com.sam.controller;

import com.sam.entity.BuildingInfo;
import com.sam.entity.CommunityInfo;
import com.sam.service.IHouseService;
import com.sam.service.impl.HouseServiceImpl;
import com.sam.util.FileUploadUtil;
import com.sam.util.OSSUtil;
import com.sam.util.ResultVO;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.MultipartConfig;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.Part;

import java.io.File;
import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/8/13
 */
@WebServlet(name = "HouseServlet", value = "/HouseServlet")
@MultipartConfig(maxFileSize = 1024 * 1024 * 5) // 限制上传文件大小为5MB
public class HouseServlet extends BaseServlet {
    private IHouseService houseService = new HouseServiceImpl();

    /**
     * 插入小区
     */
    protected void insertCommunity(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");

        try {
            String cname = request.getParameter("cname");
            String caddress = request.getParameter("caddress");

            // 参数验证
            if (cname == null || cname.trim().isEmpty()) {
                response.getWriter().write("{\"code\":400,\"message\":\"小区名称不能为空\"}");
                return;
            }
            if (caddress == null || caddress.trim().isEmpty()) {
                response.getWriter().write("{\"code\":400,\"message\":\"小区地址不能为空\"}");
                return;
            }

            cname = cname.trim();
            caddress = caddress.trim();

            // 检查小区名称是否已存在
            ResultVO<?> existingCommunities = houseService.findAllCommunity();
            if (existingCommunities.getCode() == 200 && existingCommunities.getData() != null) {
                java.util.List<CommunityInfo> communities = (java.util.List<CommunityInfo>) existingCommunities.getData();
                for (CommunityInfo community : communities) {
                    if (cname.equals(community.getCname())) {
                        response.getWriter().write("{\"code\":400,\"message\":\"小区名称已存在，请使用其他名称\"}");
                        return;
                    }
                    if (caddress.equals(community.getCaddress())) {
                        response.getWriter().write("{\"code\":400,\"message\":\"小区地址已存在，请使用其他地址\"}");
                        return;
                    }
                }
            }

            // 处理图片上传
            String cpic = "";
            Part part = request.getPart("cpic");
            if (Objects.nonNull(part) && part.getSize() > 0) {
                try {
                    String relativePath = FileUploadUtil.upload(request.getContextPath(), part);
                    String localFilePath = FileUploadUtil.UPLOAD_ROOT + relativePath;
                    File file = new File(localFilePath);

                    if (file.exists()) {
                        String fileName = relativePath.substring(relativePath.lastIndexOf("/") + 1);
                        cpic = OSSUtil.upload(fileName, file.getAbsolutePath());
                        System.out.println("✅ OSS上传成功，地址: " + cpic);
                    }
                } catch (Exception e) {
                    System.err.println("❌ 图片上传失败: " + e.getMessage());
                    // 图片上传失败不影响小区添加
                }
            }

            // 创建小区对象
            CommunityInfo communityInfo = new CommunityInfo();
            communityInfo.setCname(cname);
            communityInfo.setCpic(cpic);
            communityInfo.setCaddress(caddress);

            // 保存到数据库
            ResultVO<?> result = houseService.insertCommunity(communityInfo);
            response.getWriter().write(com.alibaba.fastjson2.JSON.toJSONString(result));

        } catch (Exception e) {
            System.err.println("插入小区失败: " + e.getMessage());
            e.printStackTrace();
            response.getWriter().write("{\"code\":500,\"message\":\"服务器内部错误\"}");
        }
    }

    /**
     * 查询所有小区信息
     */
    protected void findAllCommunity(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");

        try {
            ResultVO<?> result = houseService.findAllCommunity();
            if (result.getCode() == 200) {
                String jsonData = com.alibaba.fastjson2.JSON.toJSONString(result.getData());
                response.getWriter().write(jsonData);
            } else {
                response.getWriter().write("[]");
            }
        } catch (Exception e) {
            System.err.println("查询小区信息失败: " + e.getMessage());
            e.printStackTrace();
            response.getWriter().write("[]");
        }
    }

    /**
     * 根据小区ID查询楼栋
     */
    protected void findBuildingByCommunityId(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");

        try {
            String cidStr = request.getParameter("cid");
            if (cidStr == null || cidStr.trim().isEmpty()) {
                response.getWriter().write("{\"code\":400,\"message\":\"小区ID不能为空\"}");
                return;
            }

            Integer cid = Integer.parseInt(cidStr);
            ResultVO<?> result = houseService.findBuildingByCommunityId(cid);

            if (result.getCode() == 200) {
                String jsonData = com.alibaba.fastjson2.JSON.toJSONString(result.getData());
                response.getWriter().write(jsonData);
            } else {
                response.getWriter().write("[]");
            }

        } catch (NumberFormatException e) {
            response.getWriter().write("{\"code\":400,\"message\":\"小区ID格式错误\"}");
        } catch (Exception e) {
            System.err.println("查询楼栋信息失败: " + e.getMessage());
            e.printStackTrace();
            response.getWriter().write("[]");
        }
    }

    /**
     * 删除小区
     */
    protected void deleteCommunity(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");

        try {
            String cidStr = request.getParameter("cid");
            if (cidStr == null || cidStr.trim().isEmpty()) {
                response.getWriter().write("{\"code\":400,\"message\":\"小区ID不能为空\"}");
                return;
            }

            Integer cid = Integer.parseInt(cidStr);

            // 检查该小区下是否还有楼栋
            ResultVO<?> buildingResult = houseService.findBuildingByCommunityId(cid);
            if (buildingResult.getCode() == 200 && buildingResult.getData() != null) {
                java.util.List<BuildingInfo> buildings = (java.util.List<BuildingInfo>) buildingResult.getData();
                if (!buildings.isEmpty()) {
                    response.getWriter().write("{\"code\":400,\"message\":\"该小区下还有楼栋，请先删除所有楼栋后再删除小区\"}");
                    return;
                }
            }

            ResultVO<?> result = houseService.deleteCommunity(cid);
            response.getWriter().write(com.alibaba.fastjson2.JSON.toJSONString(result));

        } catch (NumberFormatException e) {
            response.getWriter().write("{\"code\":400,\"message\":\"小区ID格式错误\"}");
        } catch (Exception e) {
            System.err.println("删除小区失败: " + e.getMessage());
            e.printStackTrace();
            response.getWriter().write("{\"code\":500,\"message\":\"服务器内部错误\"}");
        }
    }

    /**
     * 插入楼栋
     */
    protected void insertBuilding(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");

        try {
            String did = request.getParameter("did");
            String cidStr = request.getParameter("cid");
            String heightStr = request.getParameter("height");

            // 参数验证
            if (did == null || did.trim().isEmpty()) {
                response.getWriter().write("{\"code\":400,\"message\":\"楼栋编号不能为空\"}");
                return;
            }
            if (cidStr == null || cidStr.trim().isEmpty()) {
                response.getWriter().write("{\"code\":400,\"message\":\"小区ID不能为空\"}");
                return;
            }
            if (heightStr == null || heightStr.trim().isEmpty()) {
                response.getWriter().write("{\"code\":400,\"message\":\"楼栋层高不能为空\"}");
                return;
            }

            did = did.trim();
            Integer cid = Integer.parseInt(cidStr);
            Integer height = Integer.parseInt(heightStr);

            // 验证层高不能为负数或零
            if (height <= 0) {
                response.getWriter().write("{\"code\":400,\"message\":\"楼栋层高必须大于0\"}");
                return;
            }

            // 检查楼栋编号在该小区内是否已存在
            ResultVO<?> existingBuildings = houseService.findBuildingByCommunityId(cid);
            if (existingBuildings.getCode() == 200 && existingBuildings.getData() != null) {
                java.util.List<BuildingInfo> buildings = (java.util.List<BuildingInfo>) existingBuildings.getData();
                for (BuildingInfo building : buildings) {
                    if (did.equals(building.getDid())) {
                        response.getWriter().write("{\"code\":400,\"message\":\"该小区内楼栋编号已存在，请使用其他编号\"}");
                        return;
                    }
                }
            }

            // 创建楼栋对象
            BuildingInfo buildingInfo = new BuildingInfo();
            buildingInfo.setDid(did);
            buildingInfo.setCid(cid);
            buildingInfo.setHeight(height);

            // 保存到数据库
            ResultVO<?> result = houseService.insertBuilding(buildingInfo);
            response.getWriter().write(com.alibaba.fastjson2.JSON.toJSONString(result));

        } catch (NumberFormatException e) {
            response.getWriter().write("{\"code\":400,\"message\":\"数字格式错误，请检查小区ID和层高\"}");
        } catch (Exception e) {
            System.err.println("插入楼栋失败: " + e.getMessage());
            e.printStackTrace();
            response.getWriter().write("{\"code\":500,\"message\":\"服务器内部错误\"}");
        }
    }

    /**
     * 删除楼栋
     */
    protected void deleteBuilding(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");

        try {
            String did = request.getParameter("did");
            String cidStr = request.getParameter("cid");

            if (did == null || did.trim().isEmpty()) {
                response.getWriter().write("{\"code\":400,\"message\":\"楼栋编号不能为空\"}");
                return;
            }
            if (cidStr == null || cidStr.trim().isEmpty()) {
                response.getWriter().write("{\"code\":400,\"message\":\"小区ID不能为空\"}");
                return;
            }

            Integer cid = Integer.parseInt(cidStr);
            ResultVO<?> result = houseService.deleteBuilding(did.trim(), cid);
            response.getWriter().write(com.alibaba.fastjson2.JSON.toJSONString(result));

        } catch (NumberFormatException e) {
            response.getWriter().write("{\"code\":400,\"message\":\"小区ID格式错误\"}");
        } catch (Exception e) {
            System.err.println("删除楼栋失败: " + e.getMessage());
            e.printStackTrace();
            response.getWriter().write("{\"code\":500,\"message\":\"服务器内部错误\"}");
        }
    }
}