package com.sam.controller;

import com.alibaba.fastjson2.JSON;
import com.sam.entity.BuildingInfo;
import com.sam.entity.CommunityInfo;
import com.sam.service.IHouseService;
import com.sam.service.impl.HouseServiceImpl;
import com.sam.util.FileUploadUtil;
import com.sam.util.OSSUtil;
import com.sam.util.ResultVO;
import com.sam.util.ResultCode;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.MultipartConfig;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.Part;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/8/13
 */
@WebServlet(name = "HouseServlet", value = "/HouseServlet")
@MultipartConfig(maxFileSize = 1024 * 1024 * 5) // 限制上传文件大小为5MB
public class HouseServlet extends BaseServlet {
    private IHouseService houseService = new HouseServiceImpl();

    /**
     * 插入小区
     */
    protected void insertCommunity(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");

        try {
            String cname = request.getParameter("cname");
            String caddress = request.getParameter("caddress");
            cname = cname.trim();
            caddress = caddress.trim();

            // 检查重复性
            if (checkCommunityExists(cname, caddress)) {
                writeResult(response, ResultVO.error(ResultCode.PARAM_ERROR.getCode(), "小区名称或地址已存在"));
                return;
            }

            // 处理图片上传
            String cpic = handleImageUpload(request);

            // 创建小区对象
            CommunityInfo communityInfo = new CommunityInfo();
            communityInfo.setCname(cname);
            communityInfo.setCpic(cpic);
            communityInfo.setCaddress(caddress);

            // 保存到数据库
            ResultVO<?> result = houseService.insertCommunity(communityInfo);
            writeResult(response, result);

        } catch (Exception e) {
            System.err.println("插入小区失败: " + e.getMessage());
            e.printStackTrace();
            writeResult(response, ResultVO.error(ResultCode.INTERNAL_ERROR.getCode(), "服务器内部错误"));
        }
    }

    /**
     * 查询所有小区信息
     */
    protected void findAllCommunity(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");

        try {
            ResultVO<List<CommunityInfo>> result = houseService.findAllCommunity();
            writeResult(response, result);
        } catch (Exception e) {
            System.err.println("查询小区信息失败: " + e.getMessage());
            e.printStackTrace();
            writeResult(response, ResultVO.error(ResultCode.INTERNAL_ERROR.getCode(), "查询失败"));
        }
    }

    /**
     * 根据小区ID查询楼栋
     */
    protected void findBuildingByCommunityId(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");

        try {
            String cidStr = request.getParameter("cid");
            if (cidStr == null || cidStr.trim().isEmpty()) {
                writeResult(response, ResultVO.error(ResultCode.PARAM_ERROR.getCode(), "小区ID不能为空"));
                return;
            }

            Integer cid = Integer.parseInt(cidStr);
            ResultVO<List<BuildingInfo>> result = houseService.findBuildingByCommunityId(cid);
            writeResult(response, result);

        } catch (NumberFormatException e) {
            writeResult(response, ResultVO.error(ResultCode.PARAM_ERROR.getCode(), "小区ID格式错误"));
        } catch (Exception e) {
            System.err.println("查询楼栋信息失败: " + e.getMessage());
            e.printStackTrace();
            writeResult(response, ResultVO.error(ResultCode.INTERNAL_ERROR.getCode(), "查询失败"));
        }
    }

    /**
     * 删除小区
     */
    protected void deleteCommunity(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");

        try {
            String cidStr = request.getParameter("cid");
            if (cidStr == null || cidStr.trim().isEmpty()) {
                writeResult(response, ResultVO.error(ResultCode.PARAM_ERROR.getCode(), "小区ID不能为空"));
                return;
            }

            Integer cid = Integer.parseInt(cidStr);

            // 检查该小区下是否还有楼栋
            ResultVO<List<BuildingInfo>> buildingResult = houseService.findBuildingByCommunityId(cid);
            if (buildingResult.getCode() == 200 && buildingResult.getData() != null && !buildingResult.getData().isEmpty()) {
                writeResult(response, ResultVO.error(ResultCode.PARAM_ERROR.getCode(), "该小区下还有楼栋，请先删除所有楼栋后再删除小区"));
                return;
            }

            ResultVO<?> result = houseService.deleteCommunity(cid);
            writeResult(response, result);

        } catch (NumberFormatException e) {
            writeResult(response, ResultVO.error(ResultCode.PARAM_ERROR.getCode(), "小区ID格式错误"));
        } catch (Exception e) {
            System.err.println("删除小区失败: " + e.getMessage());
            e.printStackTrace();
            writeResult(response, ResultVO.error(ResultCode.INTERNAL_ERROR.getCode(), "服务器内部错误"));
        }
    }

    /**
     * 插入楼栋
     */
    protected void insertBuilding(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");

        try {
            String did = request.getParameter("did");
            String cidStr = request.getParameter("cid");
            String heightStr = request.getParameter("height");

            // 后端验证（前端已验证，这里作为安全保障）
            if (did == null || did.trim().isEmpty()) {
                writeResult(response, ResultVO.error(ResultCode.PARAM_ERROR.getCode(), "楼栋编号不能为空"));
                return;
            }
            if (cidStr == null || cidStr.trim().isEmpty()) {
                writeResult(response, ResultVO.error(ResultCode.PARAM_ERROR.getCode(), "小区ID不能为空"));
                return;
            }
            if (heightStr == null || heightStr.trim().isEmpty()) {
                writeResult(response, ResultVO.error(ResultCode.PARAM_ERROR.getCode(), "楼栋层高不能为空"));
                return;
            }

            did = did.trim();
            Integer cid = Integer.parseInt(cidStr);
            Integer height = Integer.parseInt(heightStr);

            // 验证层高
            if (height <= 0) {
                writeResult(response, ResultVO.error(ResultCode.PARAM_ERROR.getCode(), "楼栋层高必须大于0"));
                return;
            }

            // 检查楼栋编号重复性
            if (checkBuildingExists(did, cid)) {
                writeResult(response, ResultVO.error(ResultCode.PARAM_ERROR.getCode(), "该小区内楼栋编号已存在"));
                return;
            }

            // 创建楼栋对象
            BuildingInfo buildingInfo = new BuildingInfo();
            buildingInfo.setDid(did);
            buildingInfo.setCid(cid);
            buildingInfo.setHeight(height);

            // 保存到数据库
            ResultVO<?> result = houseService.insertBuilding(buildingInfo);
            writeResult(response, result);

        } catch (NumberFormatException e) {
            writeResult(response, ResultVO.error(ResultCode.PARAM_ERROR.getCode(), "数字格式错误"));
        } catch (Exception e) {
            System.err.println("插入楼栋失败: " + e.getMessage());
            e.printStackTrace();
            writeResult(response, ResultVO.error(ResultCode.INTERNAL_ERROR.getCode(), "服务器内部错误"));
        }
    }

    /**
     * 删除楼栋
     */
    protected void deleteBuilding(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");

        try {
            String did = request.getParameter("did");
            String cidStr = request.getParameter("cid");

            if (did == null || did.trim().isEmpty()) {
                writeResult(response, ResultVO.error(ResultCode.PARAM_ERROR.getCode(), "楼栋编号不能为空"));
                return;
            }
            if (cidStr == null || cidStr.trim().isEmpty()) {
                writeResult(response, ResultVO.error(ResultCode.PARAM_ERROR.getCode(), "小区ID不能为空"));
                return;
            }

            Integer cid = Integer.parseInt(cidStr);
            ResultVO<?> result = houseService.deleteBuilding(did.trim(), cid);
            writeResult(response, result);

        } catch (NumberFormatException e) {
            writeResult(response, ResultVO.error(ResultCode.PARAM_ERROR.getCode(), "小区ID格式错误"));
        } catch (Exception e) {
            System.err.println("删除楼栋失败: " + e.getMessage());
            e.printStackTrace();
            writeResult(response, ResultVO.error(ResultCode.INTERNAL_ERROR.getCode(), "服务器内部错误"));
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 统一写入响应结果
     */
    private void writeResult(HttpServletResponse response, ResultVO<?> result) throws IOException {
        response.getWriter().write(JSON.toJSONString(result));
    }

    /**
     * 检查小区是否已存在
     */
    private boolean checkCommunityExists(String cname, String caddress) {
        try {
            ResultVO<List<CommunityInfo>> result = houseService.findAllCommunity();
            if (result.getCode() == 200 && result.getData() != null) {
                for (CommunityInfo community : result.getData()) {
                    if (cname.equals(community.getCname()) || caddress.equals(community.getCaddress())) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("检查小区重复性失败: " + e.getMessage());
        }
        return false;
    }

    /**
     * 检查楼栋是否已存在
     */
    private boolean checkBuildingExists(String did, Integer cid) {
        try {
            ResultVO<List<BuildingInfo>> result = houseService.findBuildingByCommunityId(cid);
            if (result.getCode() == 200 && result.getData() != null) {
                for (BuildingInfo building : result.getData()) {
                    if (did.equals(building.getDid())) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("检查楼栋重复性失败: " + e.getMessage());
        }
        return false;
    }

    /**
     * 处理图片上传
     */
    private String handleImageUpload(HttpServletRequest request) {
        try {
            Part part = request.getPart("cpic");
            if (Objects.nonNull(part) && part.getSize() > 0) {
                String relativePath = FileUploadUtil.upload(request.getContextPath(), part);
                String localFilePath = FileUploadUtil.UPLOAD_ROOT + relativePath;
                File file = new File(localFilePath);

                if (file.exists()) {
                    String fileName = relativePath.substring(relativePath.lastIndexOf("/") + 1);
                    String ossUrl = OSSUtil.upload(fileName, file.getAbsolutePath());
                    System.out.println("✅ OSS上传成功，地址: " + ossUrl);
                    return ossUrl;
                }
            }
        } catch (Exception e) {
            System.err.println("❌ 图片上传失败: " + e.getMessage());
        }
        return "";
    }
}