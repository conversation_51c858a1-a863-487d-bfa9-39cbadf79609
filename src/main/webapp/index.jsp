<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>房屋管理系统</title>

    <style>
        body{

            /* 设置背景图片*/
            background-image: url("static/img/indexBgp.jpg");
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            /*设计整体页面样式*/
            margin: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: Arial, sans-serif;
        }

        /* 玻璃质感表单框 */
        .glass-form {
            width: 350px;
            padding: 30px;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.2); /* 半透明背景 */
            backdrop-filter: blur(10px);           /* 毛玻璃效果 */
            -webkit-backdrop-filter: blur(10px);   /* Safari 兼容 */
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
        }

        .glass-form h2 {
            color: #a24b58;
            margin-bottom: 20px;
        }

        .glass-form input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: none;
            border-radius: 10px;
            outline: none;
        }

        .glass-form button {
            width: 100%;
            padding: 12px;
            margin-top: 15px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.4);
            color: #000;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .glass-form button:hover {
            background: rgba(255, 255, 255, 0.7);
        }

    </style>

</head>
<body>

<div class="glass-form">
    <h2>欢迎使用房屋管理系统</h2>
    <form>
        <button type="button" onclick="location.href='PageServlet?to=CInfo_jsp'">开始小区管理</button>
        <button type="button" onclick="window.location.href='https://www.bilibili.com/video/BV1GK7GzUEqB/?spm_id_from=333.337.search-card.all.click&vd_source=2c936e397dc81209fc5b9cc350222901'">堵桥来</button>
    </form>
</div>

</body>
</html>