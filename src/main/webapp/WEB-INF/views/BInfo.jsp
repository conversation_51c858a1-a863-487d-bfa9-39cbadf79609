<%@ page contentType="text/html;charset=UTF-8" language="java" isELIgnored="false" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<html>
<head>
    <title>楼栋信息管理</title>
    <script src="${pageContext.request.contextPath}/static/js/jquery-3.6.3.js"></script>
    <style>
        body {
            margin: 0;
            min-height: 100vh;
            padding: 20px 0;
            font-family: "Helvetica Neue", Arial, sans-serif;
            /* 设置背景图片*/
            background-image: url("${pageContext.request.contextPath}/static/img/communityBgp.jpg");
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            background-attachment: fixed;
            overflow-y: auto; /* 允许垂直滚动 */
        }

        .container {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;
            min-height: calc(100vh - 40px);
            gap: 30px;
            padding: 20px;
        }

        /* 毛玻璃表单 */
        .glass-form {
            width: 400px;
            max-width: 90vw;
            padding: 25px;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
            margin: 0 auto 30px auto;
            align-self: center;
        }

        .glass-form h2 {
            color: #fff;
            margin-bottom: 20px;
        }

        .glass-form input[type="text"],
        .glass-form input[type="number"] {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: none;
            border-radius: 10px;
            outline: none;
        }

        .glass-form input[readonly] {
            background: rgba(255, 255, 255, 0.3);
            cursor: not-allowed;
        }

        /* 毛玻璃按钮 + 渐变发光效果 */
        .glass-form button {
            width: 100%;
            padding: 12px;
            margin-top: 15px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(135deg, rgba(255,255,255,0.4), rgba(255,255,255,0.6));
            color: #000;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 0 10px rgba(255,255,255,0.5);
        }

        .glass-form button:hover {
            background: linear-gradient(135deg, rgba(255,255,255,0.7), rgba(255,255,255,0.9));
            box-shadow: 0 0 20px rgba(255,255,255,0.8);
            transform: scale(1.05);
        }

        hr {
            width: 80%;
            border: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.5);
            margin: 20px auto;
        }

        /* MacOS 风格楼栋列表 */
        .building-list {
            width: 80%;
            max-width: 800px;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .building-list .title-bar {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: rgba(240, 240, 240, 0.8);
            border-bottom: 1px solid rgba(200, 200, 200, 0.6);
        }

        .title-bar .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 6px;
        }
        .dot.red { background: #ff5f56; }
        .dot.yellow { background: #ffbd2e; }
        .dot.green { background: #27c93f; }

        table {
            width: 100%;
            border-collapse: collapse;
            text-align: left;
        }
        th, td {
            padding: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.5);
        }
        th {
            background: rgba(255, 255, 255, 0.4);
        }

        /* 删除按钮样式 */
        .delete-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            background: rgba(255, 100, 100, 0.7);
            color: #fff;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .delete-btn:hover {
            background: rgba(255, 80, 80, 1);
        }
    </style>
</head>
<body>
<div class="container">

<!-- 毛玻璃楼栋表单 -->
<div class="glass-form">
    <h2>添加楼栋</h2>
    <form id="buildingForm">
        <input type="text" id="did" name="did" placeholder="输入楼栋编号" required>
        <input type="number" id="height" name="height" placeholder="输入楼栋层高（层）" min="1" required>
        <input type="text" id="cid" name="cid" readonly>
        <button type="submit">添加楼栋</button>
        <button type="button" onclick="goBack()" style="background: rgba(200,200,200,0.6); margin-top: 10px;">返回小区列表</button>
    </form>
</div>

<hr>

<!-- 已存在楼栋信息列表 -->
<div class="building-list">
    <div class="title-bar">
        <div class="dot red"></div>
        <div class="dot yellow"></div>
        <div class="dot green"></div>
        <span style="margin-left: 10px; font-weight: bold;">楼栋信息</span>
    </div>
    <table id="buildingTable">
        <thead>
        <tr>
            <th>楼栋编号</th>
            <th>楼栋层高</th>
            <th>所属小区ID</th>
            <th>操作</th>
        </tr>
        </thead>
        <tbody id="buildingTableBody">
        <!-- 动态加载楼栋数据 -->
        </tbody>
    </table>
</div>
</div> <!-- 关闭container -->

<script>
    $(document).ready(function(){
        // 获取URL参数中的小区ID
        const urlParams = new URLSearchParams(window.location.search);
        const communityId = urlParams.get('cid');

        if (communityId) {
            $("#cid").val(communityId);
            loadBuildingList(communityId);
        } else {
            alert("缺少小区ID参数");
            window.history.back();
        }

        // 添加楼栋信息
        $("#buildingForm").on("submit", function(e){
            e.preventDefault();

            const did = $("#did").val().trim();
            const height = $("#height").val();
            const cid = $("#cid").val();

            // 前端验证
            if (!validateBuildingForm(did, height)) {
                return;
            }

            // Ajax验证重复性
            validateBuildingUnique(did, cid, function(isValid, message) {
                if (!isValid) {
                    alert(message);
                    return;
                }

                // 验证通过，提交表单
                submitBuildingForm(did, height, cid);
            });
        });

        // 删除楼栋信息（事件委托，支持动态添加的行）
        $("#buildingTable").on("click", ".delete-btn", function(){
            const row = $(this).closest("tr");
            const did = row.find("td:first").text();
            const cid = $("#cid").val();

            if (confirm(`确定要删除楼栋【${did}】吗？`)) {
                $.ajax({
                    url: "${pageContext.request.contextPath}/HouseServlet?to=deleteBuilding",
                    type: "POST",
                    data: {
                        did: did,
                        cid: cid
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.code === 200) {
                            alert("删除成功！");
                            loadBuildingList(cid);
                        } else {
                            alert("删除失败：" + response.message);
                        }
                    },
                    error: function() {
                        alert("删除请求失败");
                    }
                });
            }
        });
    });

    // ==================== 验证函数 ====================

    /**
     * 验证楼栋表单
     */
    function validateBuildingForm(did, height) {
        if (!did) {
            alert("请输入楼栋编号");
            $("#did").focus();
            return false;
        }
        if (did.length < 1 || did.length > 20) {
            alert("楼栋编号长度应在1-20个字符之间");
            $("#did").focus();
            return false;
        }
        if (!height) {
            alert("请输入楼栋层高");
            $("#height").focus();
            return false;
        }
        const heightNum = parseInt(height);
        if (isNaN(heightNum) || heightNum <= 0) {
            alert("楼栋层高必须是大于0的整数");
            $("#height").focus();
            return false;
        }
        if (heightNum > 200) {
            alert("楼栋层高不能超过200层");
            $("#height").focus();
            return false;
        }
        return true;
    }

    /**
     * Ajax验证楼栋唯一性
     */
    function validateBuildingUnique(did, cid, callback) {
        $.ajax({
            url: "${pageContext.request.contextPath}/HouseServlet?to=findBuildingByCommunityId",
            type: "GET",
            data: { cid: cid },
            dataType: "json",
            success: function(response) {
                if (response.code === 200 && response.data) {
                    for (let building of response.data) {
                        if (building.did === did) {
                            callback(false, "该小区内楼栋编号已存在，请使用其他编号");
                            return;
                        }
                    }
                }
                callback(true, "验证通过");
            },
            error: function() {
                callback(false, "验证失败，请重试");
            }
        });
    }

    /**
     * 提交楼栋表单
     */
    function submitBuildingForm(did, height, cid) {
        $.ajax({
            url: "${pageContext.request.contextPath}/HouseServlet?to=insertBuilding",
            type: "POST",
            data: {
                did: did,
                height: height,
                cid: cid
            },
            dataType: "json",
            success: function(response) {
                if (response.code === 200) {
                    alert("楼栋添加成功！");
                    // 清空表单
                    $("#did").val("");
                    $("#height").val("");
                    // 重新加载楼栋列表
                    loadBuildingList(cid);
                } else {
                    alert("添加失败：" + response.message);
                }
            },
            error: function(xhr, status, error) {
                alert("请求失败：" + error);
            }
        });
    }

    // ==================== 数据操作函数 ====================

    // 加载楼栋列表
    function loadBuildingList(cid) {
        $.ajax({
            url: "${pageContext.request.contextPath}/HouseServlet?to=findBuildingByCommunityId",
            type: "GET",
            data: { cid: cid },
            dataType: "json",
            success: function(response) {
                const tbody = $("#buildingTableBody");
                tbody.empty();

                if (response.code === 200 && response.data && response.data.length > 0) {
                    response.data.forEach(function(building) {
                        const row = `
                            <tr>
                                <td>${building.did}</td>
                                <td>${building.height}层</td>
                                <td>${building.cid}</td>
                                <td><button class="delete-btn">删除</button></td>
                            </tr>
                        `;
                        tbody.append(row);
                    });
                } else {
                    tbody.append('<tr><td colspan="4" style="text-align:center;">暂无楼栋信息</td></tr>');
                }
            },
            error: function() {
                $("#buildingTableBody").html('<tr><td colspan="4" style="text-align:center;">加载失败</td></tr>');
            }
        });
    }

    // 返回小区列表
    function goBack() {
        window.location.href = "${pageContext.request.contextPath}/HouseServlet?to=findAllCommunity";
    }
</script>

</body>
</html>
