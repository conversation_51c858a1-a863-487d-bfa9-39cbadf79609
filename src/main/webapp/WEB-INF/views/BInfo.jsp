<%@ page contentType="text/html;charset=UTF-8" language="java" isELIgnored="false" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<html>
<head>
    <title>楼栋信息管理</title>
    <script src="${pageContext.request.contextPath}/static/js/jquery-3.6.3.js"></script>
    <style>
        body {
            margin: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            font-family: "Helvetica Neue", Arial, sans-serif;
            /* 设置背景图片*/
            background-image: url("${pageContext.request.contextPath}/static/img/communityBgp.jpg");
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
        }

        /* 毛玻璃表单 */
        .glass-form {
            width: 400px;
            padding: 25px;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
            margin-bottom: 30px;
        }

        .glass-form h2 {
            color: #fff;
            margin-bottom: 20px;
        }

        .glass-form input[type="text"],
        .glass-form input[type="number"] {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: none;
            border-radius: 10px;
            outline: none;
        }

        .glass-form input[readonly] {
            background: rgba(255, 255, 255, 0.3);
            cursor: not-allowed;
        }

        /* 毛玻璃按钮 + 渐变发光效果 */
        .glass-form button {
            width: 100%;
            padding: 12px;
            margin-top: 15px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(135deg, rgba(255,255,255,0.4), rgba(255,255,255,0.6));
            color: #000;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 0 10px rgba(255,255,255,0.5);
        }

        .glass-form button:hover {
            background: linear-gradient(135deg, rgba(255,255,255,0.7), rgba(255,255,255,0.9));
            box-shadow: 0 0 20px rgba(255,255,255,0.8);
            transform: scale(1.05);
        }

        hr {
            width: 80%;
            border: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.5);
            margin: 20px auto;
        }

        /* MacOS 风格楼栋列表 */
        .building-list {
            width: 80%;
            max-width: 800px;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .building-list .title-bar {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: rgba(240, 240, 240, 0.8);
            border-bottom: 1px solid rgba(200, 200, 200, 0.6);
        }

        .title-bar .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 6px;
        }
        .dot.red { background: #ff5f56; }
        .dot.yellow { background: #ffbd2e; }
        .dot.green { background: #27c93f; }

        table {
            width: 100%;
            border-collapse: collapse;
            text-align: left;
        }
        th, td {
            padding: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.5);
        }
        th {
            background: rgba(255, 255, 255, 0.4);
        }

        /* 删除按钮样式 */
        .delete-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            background: rgba(255, 100, 100, 0.7);
            color: #fff;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .delete-btn:hover {
            background: rgba(255, 80, 80, 1);
        }
    </style>
</head>
<body>

<!-- 毛玻璃楼栋表单 -->
<div class="glass-form">
    <h2>添加楼栋</h2>
    <form id="buildingForm">
        <input type="text" id="buildingNumber" placeholder="输入楼栋编号" required>
        <input type="number" id="floorHeight" placeholder="输入楼栋层高（米）" required>
        <input type="text" id="communityId" value="12345" readonly>
        <button type="submit">添加楼栋</button>
    </form>
</div>

<hr>

<!-- 已存在楼栋信息列表 -->
<div class="building-list">
    <div class="title-bar">
        <div class="dot red"></div>
        <div class="dot yellow"></div>
        <div class="dot green"></div>
        <span style="margin-left: 10px; font-weight: bold;">楼栋信息</span>
    </div>
    <table id="buildingTable">
        <thead>
        <tr>
            <th>楼栋编号</th>
            <th>楼栋层高</th>
            <th>所属小区ID</th>
            <th>操作</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td>1号楼</td>
            <td>33</td>
            <td>12345</td>
            <td><button class="delete-btn">删除</button></td>
        </tr>
        <tr>
            <td>2号楼</td>
            <td>30</td>
            <td>12345</td>
            <td><button class="delete-btn">删除</button></td>
        </tr>
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function(){
        // 添加楼栋信息
        $("#buildingForm").on("submit", function(e){
            e.preventDefault();
            const number = $("#buildingNumber").val();
            const height = $("#floorHeight").val();
            const communityId = $("#communityId").val();

            if(number && height){
                const newRow = `<tr>
                            <td>${number}</td>
                            <td>${height}</td>
                            <td>${communityId}</td>
                            <td><button class="delete-btn">删除</button></td>
                          </tr>`;
                $("#buildingTable tbody").append(newRow);

                // 清空表单
                $("#buildingNumber").val("");
                $("#floorHeight").val("");
            }
        });

        // 删除楼栋信息（事件委托，支持动态添加的行）
        $("#buildingTable").on("click", ".delete-btn", function(){
            $(this).closest("tr").remove();
        });
    });
</script>

</body>
</html>
