<%@ page contentType="text/html;charset=UTF-8" language="java" isELIgnored="false" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<html>
<head>
    <title>楼栋信息管理</title>
    <style>
        body { font-family: Arial, sans-serif; background-color: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { text-align: center; color: #333; margin-bottom: 30px; }

        /* 导航样式 */
        .nav { margin-bottom: 20px; }
        .nav a { color: #007bff; text-decoration: none; margin-right: 10px; }
        .nav a:hover { text-decoration: underline; }

        /* 表单样式 */
        #form { text-align: center; margin-bottom: 30px; padding: 20px; background-color: #f9f9f9; border-radius: 5px; }
        #form input[type="text"], #form select { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
        #form input[type="submit"] { background-color: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        #form input[type="submit"]:hover { background-color: #218838; }

        /* 表格样式 */
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        table, th, td { border: 1px solid #ddd; }
        th { background-color: #28a745; color: white; padding: 12px; text-align: center; }
        td { padding: 10px; text-align: center; }
        tr:nth-child(even) { background-color: #f2f2f2; }
        tr:hover { background-color: #e8f4f8; }

        /* 按钮样式 */
        .btn { padding: 6px 12px; margin: 2px; border: none; border-radius: 3px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn:hover { opacity: 0.8; }

        /* 提示信息样式 */
        .tip { text-align: center; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .tip.success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .tip.error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }

        /* 小区信息显示 */
        .community-info { background-color: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>
<div class="container">
    <h1>楼栋信息管理</h1>

    <!-- 导航 -->
    <div class="nav">
        <a href="${pageContext.request.contextPath}/HouseServlet?to=findAllCommunity">← 返回小区列表</a>
    </div>

    <!-- 提示信息 -->
    <c:if test="${not empty tip}">
        <div class="tip ${tip.contains('成功') ? 'success' : 'error'}">
            ${tip}
        </div>
    </c:if>

    <!-- 显示当前小区信息 -->
    <c:if test="${not empty cid}">
        <div class="community-info">
            <strong>当前小区：</strong>
            <c:forEach var="community" items="${communityList}">
                <c:if test="${community.cid == cid}">
                    ${community.cname} (ID: ${community.cid}) - ${community.caddress}
                </c:if>
            </c:forEach>
        </div>
    </c:if>

    <!-- 新增楼栋表单 -->
    <c:if test="${not empty cid}">
        <form id="form"
              action="${pageContext.request.contextPath}/HouseServlet"
              method="post">
            <input type="hidden" name="to" value="insertBuilding">
            <input type="hidden" name="cid" value="${cid}">
            <h3>添加新楼栋</h3>
            楼栋编号：<input type="text" name="did" required placeholder="请输入楼栋编号（如：A栋、1号楼等）">
            层高：<input type="text" name="height" required placeholder="请输入层数（数字）">
            <input type="submit" value="添加楼栋">
        </form>
    </c:if>

    <!-- 楼栋信息表 -->
    <h3>楼栋列表</h3>
    <table>
        <thead>
        <tr>
            <th>楼栋编号</th>
            <th>层高</th>
            <th>所属小区</th>
            <th>操作</th>
        </tr>
        </thead>
        <tbody>
        <c:choose>
            <c:when test="${empty buildingList}">
                <tr>
                    <td colspan="4">
                        <c:choose>
                            <c:when test="${empty cid}">
                                请先选择一个小区查看楼栋信息
                            </c:when>
                            <c:otherwise>
                                该小区暂无楼栋信息
                            </c:otherwise>
                        </c:choose>
                    </td>
                </tr>
            </c:when>
            <c:otherwise>
                <c:forEach var="building" items="${buildingList}">
                    <tr>
                        <td>${building.did}</td>
                        <td>${building.height}层</td>
                        <td>
                            <c:forEach var="community" items="${communityList}">
                                <c:if test="${community.cid == building.cid}">
                                    ${community.cname}
                                </c:if>
                            </c:forEach>
                        </td>
                        <td>
                            <a href="${pageContext.request.contextPath}/HouseServlet?to=deleteBuilding&did=${building.did}&cid=${building.cid}"
                               class="btn btn-danger"
                               onclick="return confirm('确定要删除楼栋【${building.did}】吗？')">删除</a>
                        </td>
                    </tr>
                </c:forEach>
            </c:otherwise>
        </c:choose>
        </tbody>
    </table>

    <div style="text-align: center; margin-top: 20px;">
        <c:if test="${not empty cid}">
            <a href="${pageContext.request.contextPath}/HouseServlet?to=findBuildingByCommunityId&cid=${cid}" class="btn btn-primary">刷新列表</a>
        </c:if>
        <a href="${pageContext.request.contextPath}/HouseServlet?to=findAllCommunity" class="btn btn-primary">返回小区管理</a>
    </div>
</div>
</body>
</html>
