<%--
  Created by IntelliJ IDEA.
  User: sam
  Date: 2025/8/14
  Time: 19:09
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" isELIgnored="false" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>

<html>
<head>
    <title>测试页面</title>
</head>
<body>

<div class="container">
    <div class="insert-form">
        <h2>添加小区信息</h2>

        <%--ajax请求，返回ResultVO--%>
        <div class="error-message"></div>

        <%--绑定表单提交事件--%>
        <form id="submitForm">
            <div class="form-group">
                <label for="cname">小区名称：</label>
                <input type="text" id="cname" name="cname" placeholder="请输入小区名称" required>
            </div>
            <div class="form-group">
                <label for="caddress">小区地址：</label>
                <input type="text" id="caddress" name="caddress" placeholder="请输入小区地址">
            </div>
            <div class="form-group">
                <label for="cpic">小区图片：</label>
                <input type="file" id="cpic" name="cpic" accept="image/*">
            </div>
            <button type="submit" class="btn-submit">提交</button>
        </form>
    </div>
</div>

<hr>

<h2>小区列表</h2>
<table>
    <thead>
    <tr>
        <th>小区编号</th>
        <th>小区名称</th>
        <th>小区图片</th>
        <th>详细地址</th>
        <th>操作</th>
    </tr>
    </thead>
    <tbody></tbody>
</table>

<script src="${pageContext.request.contextPath}/static/js/jquery-3.6.3.js"></script>
<script>

    $(function () {
        findAllCommunity();
        // 绑定表单提交事件
        $("#submitForm").submit(function (event) {
            //阻止表单默认的提交行为
            event.preventDefault();
            // 获取表单数据
            var formData = new FormData(this);
            // 发送ajax请求
            $.ajax({
                url: "${pageContext.request.contextPath}/HouseServlet?to=insertCommunity",
                type: "post",
                data: formData,
                contentType: false,
                processData: false,
                dataType: "json",
                success: function (data) {
                    if (data.code == 200) {
                        alert("添加成功");
                    } else {
                        alert("添加失败：" + data.message);
                    }
                }
            });
        });
    });

    function findAllCommunity(){
        $.getJSON("${pageContext.request.contextPath}/HouseServlet?to=findAllCommunity", function (list){
            let html = "";
            if(list.length == 0){
                html = "<tr><td colspan='5'>暂无小区信息</td></tr>";
            } else {
                for(let i = 0; i < list.length; i++){
                    html += "<tr>";
                    html += "<td>" + list[i].cid + "</td>";
                    html += "<td>" + list[i].cname + "</td>";
                    html += "<td>" + list[i].cpic + "</td>";
                    html += "<td>" + list[i].caddress + "</td>";
                    html += "<td><a href='javascript:findBuildingByCommunityId(" + list[i].cid + ")'>查看楼栋</a></td>";
                    html += "<td><a href='javascript:deleteCommunity(" + list[i].cid + ")'>删除</a></td>";
                    html += "</tr>";
                }
            }
            $("table tbody").html(html);
        });
    }
</script>
</body>
</html>