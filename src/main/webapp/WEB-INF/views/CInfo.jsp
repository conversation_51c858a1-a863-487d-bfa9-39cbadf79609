<%--
  Created by IntelliJ IDEA.
  User: sam
  Date: 2025/8/14
  Time: 19:09
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" isELIgnored="false" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>

<html>
<head>
    <title>小区信息管理</title>

    <script src="${pageContext.request.contextPath}/static/js/jquery-3.6.3.js"></script>
    <style>
        body {
            margin: 0;
            min-height: 100vh;
            padding: 20px 0;
            background-image: url("${pageContext.request.contextPath}/static/img/buildingBgp.jpg");
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            background-attachment: fixed;
            font-family: "Helvetica Neue", Arial, sans-serif;
            /* 垂直滚动 */
            overflow-y: auto;
        }

        .container {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;
            min-height: calc(100vh - 40px);
            gap: 30px;
            padding: 20px;
        }

        /* 毛玻璃表单 */
        .glass-form {
            width: 400px;
            max-width: 90vw;
            padding: 25px;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
            margin: 0 auto 30px auto;
            align-self: center;
        }

        .glass-form h2 {
            color: #fff;
            margin-bottom: 20px;
        }

        .glass-form input[type="text"],
        .glass-form input[type="file"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: none;
            border-radius: 10px;
            outline: none;
        }

        .glass-form button {
            width: 100%;
            padding: 12px;
            margin-top: 15px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.4);
            color: #000;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .glass-form button:hover {
            background: rgba(255, 255, 255, 0.7);
        }

        .preview {
            margin-top: 10px;
        }

        .preview img {
            width: 100%;
            border-radius: 10px;
            display: none; /* 初始隐藏 */
        }

        hr {
            width: 80%;
            border: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.5);
            margin: 20px auto;
        }

        /* MacOS 风格小区列表 */
        .community-list {
            width: 80%;
            max-width: 800px;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            margin: 0 auto;
            align-self: center;
        }

        /* 模拟 MacOS 窗口的标题栏 */
        .community-list .title-bar {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: rgba(240, 240, 240, 0.8);
            border-bottom: 1px solid rgba(200, 200, 200, 0.6);
        }

        .title-bar .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 6px;
        }
        .dot.red { background: #ff5f56; }
        .dot.yellow { background: #ffbd2e; }
        .dot.green { background: #27c93f; }

        /* 小区信息表格 */
        table {
            width: 100%;
            border-collapse: collapse;
            text-align: left;
        }
        th, td {
            padding: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.5);
        }
        th {
            background: rgba(255, 255, 255, 0.4);
        }

        /* 隐藏原始 input 文件按钮 */
        input[type="file"] {
            display: none;
        }

        /* 自定义按钮，和表单按钮统一样式 */
        .custom-btn {
            width: 94%;                   /* 和 .glass-form button 一样占满宽度 */
            padding: 12px;                 /* 与表单按钮一致 */
            margin-top: 15px;              /* 与表单按钮一致 */
            border: none;
            border-radius: 10px;            /* 圆角一致 */
            background: rgba(255, 255, 255, 0.4); /* 背景一致 */
            color: #000;
            font-size: 16px;                /* 字体大小一致 */
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;   /* 与表单按钮一致的过渡效果 */
            text-align: center;             /* 居中显示文本 */
            display: inline-block;          /* 保留 inline-block 支持点击 */
        }

        .custom-btn:hover {
            background: rgba(255, 255, 255, 0.7); /* hover 效果和表单按钮一样 */
        }


        </style>
</head>
<body>
<div class="container">
<%--表单--%>
<div class="glass-form">
    <h2>添加小区</h2>
    <form id="communityForm" enctype="multipart/form-data">
        <input type="text" id="cname" name="cname" placeholder="输入小区名称" required>
        <input type="text" id="caddress" name="caddress" placeholder="输入小区地址" required>

        <div class="file-upload">
            <label for="cpic" class="custom-btn">选择图片</label>
            <input type="file" id="cpic" name="cpic" accept="image/*">
        </div>
        <div class="preview">
            <img id="previewImg" alt="图片预览">
        </div>

        <button type="submit">添加小区</button>
    </form>
</div>

<hr>

<%--小区信息--%>
<div class="community-list">
    <div class="title-bar">
        <div class="dot red"></div>
        <div class="dot yellow"></div>
        <div class="dot green"></div>
        <span style="margin-left: 10px; font-weight: bold;">小区信息</span>
    </div>
    <table id="communityTable">
        <thead>
        <tr>
            <th>小区名称</th>
            <th>小区地址</th>
            <th>照片</th>
            <th>操作</th>
        </tr>
        </thead>
        <tbody id="communityTableBody">
        </tbody>
    </table>
</div>
</div> <!-- 关闭container -->

<script>
    $(document).ready(function(){
        // 页面加载时获取小区列表
        loadCommunityList();

        // 图片预览
        $("#cpic").on("change", function(event) {
            const file = event.target.files[0];
            if (file) {
                // 验证文件大小（5MB）
                if (file.size > 5 * 1024 * 1024) {
                    alert("图片文件大小不能超过5MB");
                    $(this).val("");
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    $("#previewImg").attr("src", e.target.result).show();
                };
                reader.readAsDataURL(file);
            }
        });

        // 添加小区信息
        $("#communityForm").on("submit", function(e) {
            e.preventDefault();

            const cname = $("#cname").val().trim();
            const caddress = $("#caddress").val().trim();

            // 前端验证
            if (!validateCommunityForm(cname, caddress)) {
                return;
            }

            // Ajax验证重复性
            validateCommunityUnique(cname, caddress, function(isValid, message) {
                if (!isValid) {
                    alert(message);
                    return;
                }

                // 验证通过，提交表单
                submitCommunityForm();
            });
        });
    });

    // ==================== 验证函数 ====================

    /**
     * 验证小区表单
     */
    function validateCommunityForm(cname, caddress) {
        if (!cname) {
            alert("请输入小区名称");
            $("#cname").focus();
            return false;
        }
        if (cname.length < 2 || cname.length > 50) {
            alert("小区名称长度应在2-50个字符之间");
            $("#cname").focus();
            return false;
        }
        if (!caddress) {
            alert("请输入小区地址");
            $("#caddress").focus();
            return false;
        }
        if (caddress.length < 5 || caddress.length > 200) {
            alert("小区地址长度应在5-200个字符之间");
            $("#caddress").focus();
            return false;
        }
        return true;
    }

    /**
     * Ajax验证小区唯一性
     */
    function validateCommunityUnique(cname, caddress, callback) {
        $.ajax({
            url: "${pageContext.request.contextPath}/HouseServlet?to=findAllCommunity",
            type: "GET",
            dataType: "json",
            success: function(response) {
                if (response.code === 200 && response.data) {
                    for (let community of response.data) {
                        if (community.cname === cname) {
                            callback(false, "小区名称已存在，请使用其他名称");
                            return;
                        }
                        if (community.caddress === caddress) {
                            callback(false, "小区地址已存在，请使用其他地址");
                            return;
                        }
                    }
                }
                callback(true, "验证通过");
            },
            error: function() {
                callback(false, "验证失败，请重试");
            }
        });
    }

    /**
     * 提交小区表单
     */
    function submitCommunityForm() {
        const formData = new FormData($("#communityForm")[0]);

        $.ajax({
            url: "${pageContext.request.contextPath}/HouseServlet?to=insertCommunity",
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.code === 200) {
                    alert("小区添加成功！");
                    // 清空表单
                    $("#communityForm")[0].reset();
                    $("#previewImg").hide();
                    // 重新加载小区列表
                    loadCommunityList();
                } else {
                    alert("添加失败：" + response.message);
                }
            },
            error: function(xhr, status, error) {
                alert("请求失败：" + error);
            }
        });
    }

    // ==================== 数据操作函数 ====================

    // 加载小区列表
    function loadCommunityList() {
        $.ajax({
            url: "${pageContext.request.contextPath}/HouseServlet?to=findAllCommunity",
            type: "GET",
            dataType: "json",
            success: function(response) {
                const tbody = $("#communityTableBody");
                tbody.empty();

                if (response.code === 200 && response.data && response.data.length > 0) {
                    response.data.forEach(function(community) {
                        const imageHtml = community.cpic ?
                            `<img src="${community.cpic}" style="width:80px;height:60px;border-radius:4px;" alt="小区图片">` :
                            '<span style="color:#999;">无图片</span>';

                        const row = `
                            <tr>
                                <td>${community.cname}</td>
                                <td>${community.caddress}</td>
                                <td>${imageHtml}</td>
                                <td>
                                    <a href="${pageContext.request.contextPath}/PageServlet?to=BInfo_jsp&cid=${community.cid}"
                                       style="margin-right:10px;">查看详情</a>
                                    <a href="javascript:void(0)" onclick="deleteCommunity(${community.cid}, '${community.cname}')"
                                       style="color:red;">删除</a>
                                </td>
                            </tr>
                        `;
                        tbody.append(row);
                    });
                } else {
                    tbody.append('<tr><td colspan="4" style="text-align:center;">暂无小区信息</td></tr>');
                }
            },
            error: function() {
                $("#communityTableBody").html('<tr><td colspan="4" style="text-align:center;">加载失败</td></tr>');
            }
        });
    }

    // 删除小区
    function deleteCommunity(cid, cname) {
        if (confirm(`确定要删除小区【${cname}】吗？删除后该小区下的所有楼栋也将被删除！`)) {
            $.ajax({
                url: "${pageContext.request.contextPath}/HouseServlet?to=deleteCommunity",
                type: "POST",
                data: { cid: cid },
                dataType: "json",
                success: function(response) {
                    if (response.code === 200) {
                        alert("删除成功！");
                        loadCommunityList();
                    } else {
                        alert("删除失败：" + response.message);
                    }
                },
                error: function() {
                    alert("删除请求失败");
                }
            });
        }
    }
</script>
</body>
</html>