<%--
  Created by IntelliJ IDEA.
  User: sam
  Date: 2025/8/14
  Time: 19:09
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" isELIgnored="false" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>

<html>
<head>
    <title>小区信息管理</title>

    <script src="${pageContext.request.contextPath}/static/js/jquery-3.6.3.js"></script>
    <style>
        body {
            margin: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            background-image: url("${pageContext.request.contextPath}/static/img/buildingBgp.jpg");
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            font-family: "Helvetica Neue", Arial, sans-serif;
        }

        /* 毛玻璃表单 */
        .glass-form {
            width: 400px;
            padding: 25px;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
            margin-bottom: 30px;
        }

        .glass-form h2 {
            color: #fff;
            margin-bottom: 20px;
        }

        .glass-form input[type="text"],
        .glass-form input[type="file"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: none;
            border-radius: 10px;
            outline: none;
        }

        .glass-form button {
            width: 100%;
            padding: 12px;
            margin-top: 15px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.4);
            color: #000;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .glass-form button:hover {
            background: rgba(255, 255, 255, 0.7);
        }

        .preview {
            margin-top: 10px;
        }

        .preview img {
            width: 100%;
            border-radius: 10px;
            display: none; /* 初始隐藏 */
        }

        hr {
            width: 80%;
            border: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.5);
            margin: 20px auto;
        }

        /* MacOS 风格小区列表 */
        .community-list {
            width: 80%;
            max-width: 800px;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        /* 模拟 MacOS 窗口的标题栏 */
        .community-list .title-bar {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: rgba(240, 240, 240, 0.8);
            border-bottom: 1px solid rgba(200, 200, 200, 0.6);
        }

        .title-bar .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 6px;
        }
        .dot.red { background: #ff5f56; }
        .dot.yellow { background: #ffbd2e; }
        .dot.green { background: #27c93f; }

        /* 小区信息表格 */
        table {
            width: 100%;
            border-collapse: collapse;
            text-align: left;
        }
        th, td {
            padding: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.5);
        }
        th {
            background: rgba(255, 255, 255, 0.4);
        }

        /* 隐藏原始 input 文件按钮 */
        input[type="file"] {
            display: none;
        }

        /* 自定义按钮，和表单按钮统一样式 */
        .custom-btn {
            width: 94%;                   /* 和 .glass-form button 一样占满宽度 */
            padding: 12px;                 /* 与表单按钮一致 */
            margin-top: 15px;              /* 与表单按钮一致 */
            border: none;
            border-radius: 10px;            /* 圆角一致 */
            background: rgba(255, 255, 255, 0.4); /* 背景一致 */
            color: #000;
            font-size: 16px;                /* 字体大小一致 */
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;   /* 与表单按钮一致的过渡效果 */
            text-align: center;             /* 居中显示文本 */
            display: inline-block;          /* 保留 inline-block 支持点击 */
        }

        .custom-btn:hover {
            background: rgba(255, 255, 255, 0.7); /* hover 效果和表单按钮一样 */
        }


        </style>
</head>
<body>
<!-- 毛玻璃表单 -->
<div class="glass-form">
    <h2>添加小区</h2>
    <form id="communityForm">
        <input type="text" id="name" placeholder="输入小区名称" required>
        <input type="text" id="address" placeholder="输入小区地址" required>

        <div class="file-upload">
            <label for="photo" class="custom-btn">选择图片</label>
            <input type="file" id="photo" accept="image/*">
        </div>
        <div class="preview">
            <img id="previewImg" alt="图片预览">
        </div>

        <button type="submit">添加小区</button>
    </form>
</div>

<!-- 分割线 -->
<hr>

<!-- 已存在小区信息列表 -->
<div class="community-list">
    <div class="title-bar">
        <div class="dot red"></div>
        <div class="dot yellow"></div>
        <div class="dot green"></div>
        <span style="margin-left: 10px; font-weight: bold;">小区信息</span>
    </div>
    <table id="communityTable">
        <thead>
        <tr>
            <th>小区名称</th>
            <th>小区地址</th>
            <th>照片</th>
            <th>操作</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td>阳光花园</td>
            <td>湖北省黄冈市黄州区</td>
            <td><img src=""></td>
            <td><a href="PageServlet?to=BInfo_jsp">查看详情</a></td>
        </tr>
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function(){
        // 图片预览
        $("#photo").on("change", function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    $("#previewImg").attr("src", e.target.result).show();
                };
                reader.readAsDataURL(file);
            }
        });

        // 添加小区信息
        $("#communityForm").on("submit", function(e) {
            e.preventDefault();
            const name = $("#name").val();
            const address = $("#address").val();
            const photoSrc = $("#previewImg").attr("src") || "";

            if (name && address) {
                let newRow = `<tr>
                          <td>${name}</td>
                          <td>${address}</td>
                          <%--<td>${photoSrc ? `<img src="${photoSrc}" width="100">` : "无"}</td>--%>
                        </tr>`;
                $("#communityTable tbody").append(newRow);

                // 清空表单
                $("#name").val("");
                $("#address").val("");
                $("#photo").val("");
                $("#previewImg").hide();
            }
        });
    });
</script>
</body>
</html>