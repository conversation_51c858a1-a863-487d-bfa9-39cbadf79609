<%@ page contentType="text/html;charset=UTF-8" language="java" isELIgnored="false" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<html>
<head>
    <title>小区信息</title>
    <style>
        table { border-collapse: collapse; width: 60%; margin: 20px auto; }
        table, th, td { border: 1px solid black; }
        th, td { padding: 8px 12px; text-align: center; }
        #form { text-align: center; margin-top: 50px; line-height: 50px; }
    </style>
</head>
<body>

<!-- 新增小区表单 -->
<form id="form"
      action="${pageContext.request.contextPath}/HouseServlet"
      method="post"
      enctype="multipart/form-data">
    <input type="hidden" name="to" value="insertCommunity">
    小区名称：<input type="text" name="cname" required><br>
    小区图片：<input type="file" name="cpic" accept="image/*"><br>
    小区地址：<input type="text" name="caddress"><br>
    <input type="submit" value="新增小区">
</form>

<!-- 小区信息表 -->
<table>
    <thead>
    <tr>
        <th>小区编号</th>
        <th>小区名称</th>
        <th>小区图片</th>
        <th>详细地址</th>
        <th>操作</th>
    </tr>
    </thead>
    <tbody>
    <c:forEach var="item" items="${communityList}">
        <tr>
            <td>${item.cid}</td>
            <td>${item.cname}</td>
            <td>
                <c:if test="${not empty item.cpic}">
                    <img src="${item.cpic}" style="max-width:100px;">
                </c:if>
            </td>
            <td>${item.caddress}</td>
            <td>
                <a href="${pageContext.request.contextPath}/HouseServlet?to=viewBuildings&cid=${item.cid}">查看楼栋</a>
            </td>
        </tr>
    </c:forEach>
    </tbody>
</table>

</body>
</html>