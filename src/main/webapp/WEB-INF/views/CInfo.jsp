<%@ page contentType="text/html;charset=UTF-8" language="java" isELIgnored="false" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<html>
<head>
    <title>小区信息</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }


        #form {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }

        #form input[type="text"], #form input[type="file"] {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        #form input[type="submit"] {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        #form input[type="submit"]:hover {
            background-color: #0056b3;
        }

        /* 表格样式 */
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }

        table, th, td {
            border: 1px solid #ddd;
        }

        th {
            background-color: #007bff;
            color: white;
            padding: 12px;
            text-align: center;
        }

        td {
            padding: 10px;
            text-align: center;
        }

        tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        tr:hover {
            background-color: #e8f4f8;
        }

        /* 按钮样式 */
        .btn {
            padding: 6px 12px;
            margin: 2px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }

        .btn-info {
            background-color: #17a2b8;
            color: white;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn:hover {
            opacity: 0.8;
        }

        /* 提示信息样式 */
        .tip {
            text-align: center;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }

        .tip.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .tip.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>小区信息管理</h1>

    <!-- 提示信息 -->
    <c:if test="${not empty tip}">
        <div class="tip ${tip.contains('成功') ? 'success' : 'error'}">
                ${tip}
        </div>
    </c:if>

    <!-- 新增小区表单 -->
    <form id="form"
          action="${pageContext.request.contextPath}/HouseServlet"
          method="post"
          enctype="multipart/form-data">
        <input type="hidden" name="to" value="insertCommunity">
        <h3>添加新小区</h3>
        小区名称：<input type="text" name="cname" required placeholder="请输入小区名称"><br>
        小区图片：<input type="file" name="cpic" accept="image/*"><br>
        小区地址：<input type="text" name="caddress" placeholder="请输入小区地址"><br>
        <input type="submit" value="新增小区">
    </form>

    <!-- 小区信息表 -->
    <h3>小区列表</h3>
    <table>
        <thead>
        <tr>
            <th>小区编号</th>
            <th>小区名称</th>
            <th>小区图片</th>
            <th>详细地址</th>
            <th>操作</th>
        </tr>
        </thead>
        <tbody>
        <c:choose>
            <c:when test="${empty communityList}">
                <tr>
                    <td colspan="5">暂无小区信息</td>
                </tr>
            </c:when>
            <c:otherwise>
                <c:forEach var="item" items="${communityList}">
                    <tr>
                        <td>${item.cid}</td>
                        <td>${item.cname}</td>
                        <td>
                            <c:choose>
                                <c:when test="${not empty item.cpic}">
                                    <img src="${item.cpic}" style="max-width:80px; max-height:60px; border-radius:4px;">
                                </c:when>
                                <c:otherwise>
                                    <span style="color:#999;">无图片</span>
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td>${item.caddress}</td>
                        <td>
                            <a href="${pageContext.request.contextPath}/HouseServlet?to=findBuildingByCommunityId&cid=${item.cid}"
                               class="btn btn-info">查看楼栋</a>
                            <a href="${pageContext.request.contextPath}/HouseServlet?to=deleteCommunity&cid=${item.cid}"
                               class="btn btn-danger"
                               onclick="return confirm('确定要删除小区【${item.cname}】吗？删除后该小区下的所有楼栋也将被删除！')">删除</a>
                        </td>
                    </tr>
                </c:forEach>
            </c:otherwise>
        </c:choose>
        </tbody>
    </table>

    <div style="text-align: center; margin-top: 20px;">
        <a href="${pageContext.request.contextPath}/HouseServlet?to=findAllCommunity" class="btn btn-info">刷新列表</a>
    </div>
</div>
</body>
</html>