-- 房屋管理系统数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS house_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE house_db;

-- 创建小区信息表
CREATE TABLE IF NOT EXISTS community (
    cid INT AUTO_INCREMENT PRIMARY KEY COMMENT '小区编号（主键，自增）',
    cname VARCHAR(100) NOT NULL COMMENT '小区名称',
    cpic VARCHAR(500) COMMENT '小区图片路径',
    caddress VARCHAR(200) COMMENT '小区详细地址',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小区信息表';

-- 创建楼栋信息表
CREATE TABLE IF NOT EXISTS building (
    did VARCHAR(50) NOT NULL COMMENT '楼栋编号',
    cid INT NOT NULL COMMENT '所属小区编号',
    height INT NOT NULL COMMENT '楼栋层高',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (did, cid),
    FOREIGN KEY (cid) REFERENCES community(cid) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='楼栋信息表';

-- 插入测试数据
INSERT INTO community (cname, cpic, caddress) VALUES 
('阳光花园', '', '北京市朝阳区阳光大街123号'),
('绿城小区', '', '上海市浦东新区绿城路456号'),
('蓝天家园', '', '广州市天河区蓝天路789号');

INSERT INTO building (did, cid, height) VALUES 
('A栋', 1, 18),
('B栋', 1, 20),
('C栋', 1, 15),
('1号楼', 2, 12),
('2号楼', 2, 16),
('东区1栋', 3, 25),
('东区2栋', 3, 22);

-- 查询验证数据
SELECT '=== 小区信息 ===' as info;
SELECT * FROM community;

SELECT '=== 楼栋信息 ===' as info;
SELECT b.did, b.height, c.cname as community_name 
FROM building b 
JOIN community c ON b.cid = c.cid 
ORDER BY c.cid, b.did;
